#!/usr/bin/env python3
"""
Production script to run Guardrail Agent on real agent outputs.

Usage:
    python run_production.py                           # Audit all files in test_data/
    python run_production.py my_agent_output.json     # Audit specific file in test_data/
    python run_production.py --file path/to/file.json # Audit file anywhere
"""

import sys
import os
import json
import argparse
import glob
from datetime import datetime
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

def get_files_to_audit(args):
    """Get list of files to audit based on arguments."""
    if args.file:
        # Audit specific file with full path
        if os.path.exists(args.file):
            return [args.file]
        else:
            print(f" File not found: {args.file}")
            sys.exit(1)
    
    elif args.filename:
        # Audit specific file in test_data directory
        filepath = os.path.join('test_data', args.filename)
        if os.path.exists(filepath):
            return [filepath]
        else:
            print(f" File not found in test_data/: {args.filename}")
            print(f"Available files: {', '.join(os.listdir('test_data'))}")
            sys.exit(1)
    
    else:
        # Audit all JSON files in test_data directory
        json_files = glob.glob('test_data/*.json')
        if not json_files:
            print(" No JSON files found in test_data/ directory")
            sys.exit(1)
        return sorted(json_files)

def load_agent_output(filepath):
    """Load and validate agent output from file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            agent_output = json.load(f)
        
        # Basic validation
        if 'metadata' not in agent_output:
            print(f"  Warning: {filepath} missing 'metadata' field")
        elif 'agent_type' not in agent_output['metadata']:
            print(f"  Warning: {filepath} missing 'agent_type' in metadata")
        
        return agent_output
        
    except json.JSONDecodeError as e:
        print(f" Invalid JSON in {filepath}: {e}")
        return None
    except Exception as e:
        print(f" Error loading {filepath}: {e}")
        return None

def initialize_guardrail_agent():
    """Initialize the Guardrail Agent."""
    try:
        from agents.guardrail_agent import GuardrailAgent
        agent = GuardrailAgent()
        print(" Guardrail Agent initialized successfully")
        return agent
        
    except ValueError as e:
        print(f" Error: {e}")
        print(" Please ensure your OPENAI_API_KEY is set in the .env file")
        print("   Create a .env file with: OPENAI_API_KEY=your_api_key_here")
        sys.exit(1)
    except Exception as e:
        print(f" Error initializing Guardrail Agent: {e}")
        sys.exit(1)

def audit_single_file(agent, filepath, show_details=True):
    """Audit a single agent output file."""
    print(f"\n{'='*60}")
    print(f" AUDITING: {filepath}")
    print(f"{'='*60}")
    
    # Load agent output
    agent_output = load_agent_output(filepath)
    if agent_output is None:
        return None
    
    # Show basic info
    agent_type = agent_output.get('metadata', {}).get('agent_type', 'UNKNOWN')
    task_id = agent_output.get('metadata', {}).get('task_id', 'N/A')
    print(f" Agent Type: {agent_type}")
    print(f" Task ID: {task_id}")
    
    # Run audit
    try:
        start_time = datetime.utcnow()
        audit_report = agent.audit_agent_output(agent_output)
        end_time = datetime.utcnow()
        
        processing_time = (end_time - start_time).total_seconds() * 1000
        
        # Display results
        status = audit_report.get('status', 'UNKNOWN')
        status_emoji = {
            'PASSED': '',
            'WARNING': '',
            'CRITICAL': '',
            'VALIDATION_ERROR': '',
            'ERROR': ''
        }.get(status, '')
        
        print(f"\n{status_emoji} AUDIT RESULT: {status}")
        print(f"  Processing Time: {processing_time:.2f}ms")
        
        if show_details and status != 'PASSED':
            findings = audit_report.get('findings', {})
            
            for category, issues in findings.items():
                if issues:
                    category_name = category.replace('_', ' ').title()
                    print(f"\n {category_name}:")
                    for issue in issues:
                        print(f"   • {issue}")
        
        # Show escalation if present
        if 'escalation' in audit_report:
            escalation = audit_report['escalation']
            print(f"\n ESCALATION REQUIRED:")
            print(f"   Type: {escalation.get('type', 'UNKNOWN')}")
            print(f"   Severity: {escalation.get('severity', 'UNKNOWN')}")
            print(f"   Action: {escalation.get('action_required', 'Review required')}")
        
        # Show validation errors if present
        if 'validation_errors' in audit_report:
            print(f"\n Validation Errors:")
            for error in audit_report['validation_errors']:
                print(f"   • {error}")
        
        return audit_report
        
    except Exception as e:
        print(f" Audit failed: {e}")
        return None

def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Run Guardrail Agent on real agent outputs",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python run_production.py                              # Audit all files in test_data/
    python run_production.py my_output.json               # Audit test_data/my_output.json
    python run_production.py --file /path/to/output.json  # Audit file with full path
    python run_production.py --summary                    # Show summary only
        """
    )
    
    parser.add_argument('filename', nargs='?', help='Filename in test_data/ directory to audit')
    parser.add_argument('--file', help='Full path to file to audit')
    parser.add_argument('--summary', action='store_true', help='Show summary only (less detailed output)')
    
    args = parser.parse_args()
    
    # Get files to audit
    files_to_audit = get_files_to_audit(args)
    
    # Initialize Guardrail Agent
    agent = initialize_guardrail_agent()
    
    # Audit files
    results = []
    for filepath in files_to_audit:
        result = audit_single_file(agent, filepath, show_details=not args.summary)
        if result:
            results.append({
                'file': filepath,
                'status': result.get('status', 'ERROR'),
                'agent_type': result.get('agent_type', 'UNKNOWN'),
                'escalation': 'escalation' in result
            })
    
    # Summary
    if len(files_to_audit) > 1 or args.summary:
        print(f"\n{'='*60}")
        print(f" AUDIT SUMMARY")
        print(f"{'='*60}")
        
        status_counts = {}
        escalation_count = 0
        
        for result in results:
            status = result['status']
            status_counts[status] = status_counts.get(status, 0) + 1
            if result['escalation']:
                escalation_count += 1
        
        print(f" Files Audited: {len(results)}")
        for status, count in status_counts.items():
            emoji = {'PASSED': '', 'WARNING': '', 'CRITICAL': '', 'VALIDATION_ERROR': '', 'ERROR': ''}.get(status, '❓')
            print(f"{emoji} {status}: {count}")
        
        if escalation_count > 0:
            print(f" Escalations Required: {escalation_count}")
        
        print(f"\n Log Files:")
        print(f"   • Structured: audit_logs.jsonl")
        print(f"   • Human-readable: audit_human_readable.log")

if __name__ == "__main__":
    main()
