#!/usr/bin/env python3
"""
Simple script to install Pydantic if it's not already installed.
"""

import subprocess
import sys

def install_pydantic():
    """Install Pydantic using pip."""
    try:
        import pydantic
        print("Pydantic is already installed.")
        print(f"Version: {pydantic.VERSION}")
        return True
    except ImportError:
        print("Pydantic not found. Installing...")
        
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pydantic"])
            print("Pydantic installed successfully!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"Failed to install Pydantic: {e}")
            return False

if __name__ == "__main__":
    success = install_pydantic()
    sys.exit(0 if success else 1)
