#!/usr/bin/env python3
"""
Test script to verify the Pydantic refactoring works correctly.
Run this with: python test_pydantic_refactor.py
"""

import json
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_pydantic_model():
    """Test that the Pydantic model works correctly."""
    print("Testing Pydantic AgentOutput model...")
    
    try:
        from models.agent_output import AgentOutput, Metadata
        print("✓ Successfully imported Pydantic models")
    except ImportError as e:
        print(f"✗ Failed to import Pydantic models: {e}")
        return False
    
    # Test with valid file validation data
    try:
        with open('test_data/file_validation_output.json', 'r') as f:
            file_val_data = json.load(f)
        
        agent_output = AgentOutput.parse_agent_output(file_val_data)
        print("✓ Successfully parsed file validation output")
        print(f"  - Agent type: {agent_output.metadata.agent_type}")
        print(f"  - File name: {agent_output.file_name}")
        print(f"  - Valid rows: {agent_output.valid_rows}")
        print(f"  - Invalid rows: {agent_output.invalid_rows}")
        
    except Exception as e:
        print(f"✗ Failed to parse file validation output: {e}")
        return False
    
    # Test with data validation data
    try:
        with open('test_data/data_validation_output.json', 'r') as f:
            data_val_data = json.load(f)
        
        agent_output = AgentOutput.parse_agent_output(data_val_data)
        print("✓ Successfully parsed data validation output")
        print(f"  - Agent type: {agent_output.metadata.agent_type}")
        print(f"  - Override rate: {agent_output.override_rate}")
        print(f"  - Rule violations: {len(agent_output.rule_violations) if agent_output.rule_violations else 0}")
        
    except Exception as e:
        print(f"✗ Failed to parse data validation output: {e}")
        return False
    
    # Test with parser data
    try:
        with open('test_data/parser_output.json', 'r') as f:
            parser_data = json.load(f)
        
        agent_output = AgentOutput.parse_agent_output(parser_data)
        print("✓ Successfully parsed parser output")
        print(f"  - Agent type: {agent_output.metadata.agent_type}")
        print(f"  - Parsing confidence: {agent_output.parsing_confidence}")
        print(f"  - Rules JSON: {'Present' if agent_output.rules_json else 'None'}")
        
    except Exception as e:
        print(f"✗ Failed to parse parser output: {e}")
        return False
    
    return True

def test_validation_errors():
    """Test that validation errors are properly raised."""
    print("\nTesting validation error handling...")
    
    try:
        from models.agent_output import AgentOutput
        from pydantic import ValidationError
        
        # Test with missing metadata
        try:
            invalid_data = {"some_field": "value"}
            AgentOutput.parse_agent_output(invalid_data)
            print("✗ Should have raised validation error for missing metadata")
            return False
        except ValidationError:
            print("✓ Correctly raised validation error for missing metadata")
        
        # Test with missing agent_type
        try:
            invalid_data = {
                "metadata": {
                    "agent_version": "1.0.0"
                    # missing agent_type
                },
                "file_name": "test.xlsx"
            }
            AgentOutput.parse_agent_output(invalid_data)
            print("✗ Should have raised validation error for missing agent_type")
            return False
        except ValidationError:
            print("✓ Correctly raised validation error for missing agent_type")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in validation testing: {e}")
        return False

def test_process_compliance():
    """Test that process compliance works with Pydantic models."""
    print("\nTesting process compliance with Pydantic models...")
    
    try:
        from agents.process_compliance import ProcessCompliance
        from models.agent_output import AgentOutput
        
        compliance = ProcessCompliance()
        
        # Test with valid data
        sample_data = {
            "metadata": {
                "agent_type": "FileValidationAgent",
                "processing_time_ms": 2500,
                "agent_version": "1.2.3"
            },
            "validation_coverage": 0.99
        }
        
        agent_output = AgentOutput.parse_agent_output(sample_data)
        profile = {
            "max_processing_time_ms": 3000,
            "min_coverage": 0.98,
            "expected_output_structure": ["metadata", "validation_coverage"]
        }
        
        issues = compliance.verify_process(agent_output, profile)
        if not issues:
            print("✓ Process compliance check passed with valid data")
        else:
            print(f"✗ Unexpected issues found: {issues}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error in process compliance testing: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("PYDANTIC REFACTORING VERIFICATION TESTS")
    print("=" * 60)
    
    all_passed = True
    
    # Test 1: Pydantic model functionality
    if not test_pydantic_model():
        all_passed = False
    
    # Test 2: Validation error handling
    if not test_validation_errors():
        all_passed = False
    
    # Test 3: Process compliance integration
    if not test_process_compliance():
        all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Pydantic refactoring is working correctly.")
    else:
        print("❌ SOME TESTS FAILED! Please check the errors above.")
    print("=" * 60)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
